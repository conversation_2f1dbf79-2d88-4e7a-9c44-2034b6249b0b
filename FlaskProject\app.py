from flask import Flask, jsonify
from flask_cors import CORS
import mysql.connector


app = Flask(__name__)
CORS(app)


@app.route('/api/products', methods=['GET'])
def get_products():  # put application's code here
    query = "SELECT * FROM Products"
    try:
        conn = mysql.connector.connect(user='product-api',
                                       password='IKT',
                                       host='product-db',
                                       database='product_db')
        cursor = conn.cursor(dictionary=True)
        cursor.execute(query)
        result = cursor.fetchall()
        return jsonify(result)
    except Exception as e:
        print(e)
        return jsonify({'error': 'Error fetching products'}), 500
    finally:
        cursor.close()
        conn.close()


if __name__ == '__main__':
    app.run(debug=True)
